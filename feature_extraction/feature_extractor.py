"""
Multi-Modal Feature Extraction Module
Extracts 78-dimensional feature vector from CAN bus traffic
"""
import numpy as np
from typing import List, Dict, Tuple, Optional
from collections import deque, defaultdict
from scipy import stats, signal
from dataclasses import dataclass
import time

from simulation.can_message import CANMessage

@dataclass
class FeatureVector:
    """Structured feature vector with named components"""
    timing: np.ndarray          # 24 features
    entropy: np.ndarray         # 16 features
    signal_level: np.ndarray    # 20 features
    contextual: np.ndarray      # 10 features
    frequency: np.ndarray       # 8 features

    def to_array(self) -> np.ndarray:
        """Convert to flat numpy array"""
        return np.concatenate([
            self.timing,
            self.entropy,
            self.signal_level,
            self.contextual,
            self.frequency
        ])

    @property
    def dimension(self) -> int:
        return len(self.to_array())

class TimingFeatureExtractor:
    """Extracts timing-based features from CAN messages"""

    def __init__(self, windows: List[float] = [0.05, 0.1, 0.5, 1.0, 5.0]):
        self.windows = windows
        self.message_times = defaultdict(lambda: deque(maxlen=1000))
        self.inter_arrival_times = defaultdict(lambda: deque(maxlen=999))

    def update(self, message: CANMessage):
        """Update with new message"""
        msg_id = message.id
        timestamp = message.timestamp

        # Store timestamp
        self.message_times[msg_id].append(timestamp)

        # Calculate inter-arrival time
        if len(self.message_times[msg_id]) > 1:
            iat = timestamp - self.message_times[msg_id][-2]
            self.inter_arrival_times[msg_id].append(iat)

    def extract(self, current_time: float) -> np.ndarray:
        """Extract timing features"""
        features = []

        # For each time window
        for window in self.windows:
            window_features = self._extract_window_features(current_time - window, current_time)
            features.extend(window_features)

        # Ensure we have exactly 24 features
        if len(features) < 24:
            features.extend([0.0] * (24 - len(features)))

        return np.array(features[:24])

    def _extract_window_features(self, start_time: float, end_time: float) -> List[float]:
        """Extract features for a specific time window"""
        all_iats = []
        message_counts = defaultdict(int)

        # Collect inter-arrival times in window
        for msg_id, times in self.message_times.items():
            window_times = [t for t in times if start_time <= t <= end_time]
            message_counts[msg_id] = len(window_times)

            if msg_id in self.inter_arrival_times:
                # Get IATs corresponding to this window
                window_iats = list(self.inter_arrival_times[msg_id])[-len(window_times)+1:]
                all_iats.extend(window_iats)

        # Calculate statistics
        if all_iats:
            return [
                np.mean(all_iats),           # Mean IAT
                np.var(all_iats),            # Variance IAT
                stats.skew(all_iats),        # Skewness
                stats.kurtosis(all_iats),    # Kurtosis
            ]
        else:
            return [0.0, 0.0, 0.0, 0.0]

class EntropyFeatureExtractor:
    """Extracts entropy-based features from CAN messages"""

    def __init__(self, window_size: int = 100):
        self.window_size = window_size
        self.message_buffer = deque(maxlen=window_size)
        self.id_counts = defaultdict(int)
        self.payload_bytes = defaultdict(lambda: defaultdict(int))

    def update(self, message: CANMessage):
        """Update with new message"""
        self.message_buffer.append(message)
        self.id_counts[message.id] += 1

        # Track payload byte distributions
        for i, byte in enumerate(message.data):
            self.payload_bytes[i][byte] += 1

    def extract(self) -> np.ndarray:
        """Extract entropy features"""
        features = []

        # Message ID entropy
        id_entropy = self._calculate_entropy(list(self.id_counts.values()))
        features.append(id_entropy)

        # Payload entropy (byte-level)
        for i in range(8):  # 8 bytes in CAN message
            if i in self.payload_bytes:
                byte_entropy = self._calculate_entropy(list(self.payload_bytes[i].values()))
                features.append(byte_entropy)
            else:
                features.append(0.0)

        # Bit-level entropy for first 4 bytes
        bit_entropies = self._calculate_bit_entropy()
        features.extend(bit_entropies[:7])  # 7 more features to reach 16

        return np.array(features[:16])

    def _calculate_entropy(self, counts: List[int]) -> float:
        """Calculate Shannon entropy"""
        if not counts or sum(counts) == 0:
            return 0.0

        total = sum(counts)
        probs = [c / total for c in counts]
        entropy = -sum(p * np.log2(p) if p > 0 else 0 for p in probs)

        return entropy

    def _calculate_bit_entropy(self) -> List[float]:
        """Calculate bit-level entropy"""
        bit_counts = defaultdict(lambda: defaultdict(int))

        for msg in self.message_buffer:
            for byte_idx, byte_val in enumerate(msg.data[:4]):  # First 4 bytes
                for bit_idx in range(8):
                    bit_val = (byte_val >> bit_idx) & 1
                    bit_counts[byte_idx * 8 + bit_idx][bit_val] += 1

        entropies = []
        for bit_pos in range(32):  # 32 bits
            if bit_pos in bit_counts:
                counts = list(bit_counts[bit_pos].values())
                entropy = self._calculate_entropy(counts)
                entropies.append(entropy)
            else:
                entropies.append(0.0)

        return entropies

class SignalLevelFeatureExtractor:
    """Extracts signal-level features from decoded CAN signals"""

    def __init__(self, max_signals: int = 50):
        self.max_signals = max_signals
        self.signal_values = defaultdict(lambda: deque(maxlen=100))
        self.signal_stats = {}

    def update(self, message: CANMessage):
        """Update with new message"""
        # Extract all signal values
        for signal_name, signal_def in message.signals.items():
            value = message.get_signal_value(signal_name)
            if value is not None:
                key = f"{message.id:03X}_{signal_name}"
                self.signal_values[key].append(value)

    def extract(self) -> np.ndarray:
        """Extract signal-level features"""
        features = []

        # Get top signals by update frequency
        signal_keys = sorted(self.signal_values.keys(),
                           key=lambda k: len(self.signal_values[k]),
                           reverse=True)[:self.max_signals]

        # Calculate statistics for top signals
        for key in signal_keys[:5]:  # Top 5 signals
            values = np.array(self.signal_values[key])
            if len(values) > 1:
                features.extend([
                    np.mean(values),
                    np.var(values),
                    np.min(values),
                    np.max(values)
                ])
            else:
                features.extend([0.0, 0.0, 0.0, 0.0])

        # Pad to 20 features
        while len(features) < 20:
            features.append(0.0)

        return np.array(features[:20])

class ContextualFeatureExtractor:
    """Extracts contextual features related to vehicle state"""

    def __init__(self):
        self.vehicle_states = {
            'idle': 0,
            'starting': 1,
            'accelerating': 2,
            'cruising': 3,
            'braking': 4
        }
        self.current_state = 'idle'
        self.state_history = deque(maxlen=100)
        self.environmental_factors = {}

    def update(self, vehicle_state: Dict[str, float], environmental_factors: Dict[str, float] = None):
        """Update with vehicle state information"""
        # Determine operational state
        if vehicle_state.get('rpm', 0) == 0:
            self.current_state = 'idle'
        elif vehicle_state.get('speed', 0) < 5:
            self.current_state = 'starting'
        elif vehicle_state.get('brake_pressure', 0) > 10:
            self.current_state = 'braking'
        elif abs(vehicle_state.get('acceleration', 0)) > 1:
            self.current_state = 'accelerating'
        else:
            self.current_state = 'cruising'

        self.state_history.append(self.current_state)

        if environmental_factors:
            self.environmental_factors = environmental_factors

    def extract(self) -> np.ndarray:
        """Extract contextual features"""
        features = []

        # One-hot encoding of current state
        state_vector = [0.0] * len(self.vehicle_states)
        state_vector[self.vehicle_states[self.current_state]] = 1.0
        features.extend(state_vector)

        # State transition frequencies
        if len(self.state_history) > 1:
            transitions = defaultdict(int)
            for i in range(1, len(self.state_history)):
                transition = (self.state_history[i-1], self.state_history[i])
                transitions[transition] += 1

            # Add transition probabilities for common transitions
            common_transitions = [
                ('idle', 'starting'),
                ('starting', 'accelerating'),
                ('accelerating', 'cruising'),
                ('cruising', 'braking'),
                ('braking', 'idle')
            ]

            total_transitions = sum(transitions.values())
            for trans in common_transitions:
                if total_transitions > 0:
                    prob = transitions[trans] / total_transitions
                    features.append(prob)
                else:
                    features.append(0.0)
        else:
            features.extend([0.0] * 5)

        return np.array(features[:10])

class FrequencyDomainFeatureExtractor:
    """Extracts frequency-domain features from signal time series"""

    def __init__(self, sample_rate: float = 100.0, fft_size: int = 256):
        self.sample_rate = sample_rate
        self.fft_size = fft_size
        self.signal_buffers = defaultdict(lambda: deque(maxlen=fft_size))

    def update(self, message: CANMessage):
        """Update with new message"""
        # Store signal values for FFT analysis
        for signal_name in message.signals:
            value = message.get_signal_value(signal_name)
            if value is not None:
                key = f"{message.id:03X}_{signal_name}"
                self.signal_buffers[key].append(value)

    def extract(self) -> np.ndarray:
        """Extract frequency-domain features"""
        features = []

        # Select signals with enough data
        valid_signals = [(k, v) for k, v in self.signal_buffers.items()
                        if len(v) >= self.fft_size // 2]

        if valid_signals:
            # Use first valid signal for analysis
            key, values = valid_signals[0]
            values_array = np.array(values)

            # Compute FFT
            fft_result = np.fft.fft(values_array, n=self.fft_size)
            freqs = np.fft.fftfreq(self.fft_size, d=1/self.sample_rate)

            # Get magnitude spectrum
            magnitude = np.abs(fft_result[:self.fft_size//2])
            freqs_positive = freqs[:self.fft_size//2]

            # Extract features
            if len(magnitude) > 0:
                # Dominant frequency
                dominant_freq_idx = np.argmax(magnitude[1:]) + 1  # Skip DC
                dominant_freq = freqs_positive[dominant_freq_idx]
                features.append(dominant_freq)

                # Spectral centroid
                spectral_centroid = np.sum(freqs_positive * magnitude) / np.sum(magnitude)
                features.append(spectral_centroid)

                # Spectral spread
                spectral_spread = np.sqrt(np.sum(((freqs_positive - spectral_centroid) ** 2) * magnitude) / np.sum(magnitude))
                features.append(spectral_spread)

                # Energy in frequency bands
                band_edges = [0, 10, 20, 30, 40, 50]  # Hz
                for i in range(len(band_edges) - 1):
                    band_mask = (freqs_positive >= band_edges[i]) & (freqs_positive < band_edges[i+1])
                    band_energy = np.sum(magnitude[band_mask] ** 2)
                    features.append(band_energy)
            else:
                features.extend([0.0] * 8)
        else:
            features.extend([0.0] * 8)

        return np.array(features[:8])

class MultiModalFeatureExtractor:
    """Main feature extractor combining all modalities"""

    def __init__(self, config=None):
        self.config = config or {}

        # Initialize sub-extractors
        self.timing_extractor = TimingFeatureExtractor()
        self.entropy_extractor = EntropyFeatureExtractor()
        self.signal_extractor = SignalLevelFeatureExtractor()
        self.contextual_extractor = ContextualFeatureExtractor()
        self.frequency_extractor = FrequencyDomainFeatureExtractor()

        # Message history
        self.message_history = deque(maxlen=1000)
        self.last_extraction_time = time.time()

    def update(self, message: CANMessage, vehicle_state: Optional[Dict[str, float]] = None):
        """Update extractors with new message"""
        self.message_history.append(message)

        # Update individual extractors
        self.timing_extractor.update(message)
        self.entropy_extractor.update(message)
        self.signal_extractor.update(message)
        self.frequency_extractor.update(message)

        if vehicle_state is not None:
            self.contextual_extractor.update(vehicle_state)

    def extract(self) -> FeatureVector:
        """Extract complete feature vector"""
        current_time = time.time()

        # Extract features from each domain
        timing_features = self.timing_extractor.extract(current_time)
        entropy_features = self.entropy_extractor.extract()
        signal_features = self.signal_extractor.extract()
        contextual_features = self.contextual_extractor.extract()
        frequency_features = self.frequency_extractor.extract()

        # Create structured feature vector
        feature_vector = FeatureVector(
            timing=timing_features,
            entropy=entropy_features,
            signal_level=signal_features,
            contextual=contextual_features,
            frequency=frequency_features
        )

        self.last_extraction_time = current_time

        return feature_vector

    def get_feature_names(self) -> List[str]:
        """Get names for all features"""
        names = []

        # Timing features
        for window in self.timing_extractor.windows:
            names.extend([
                f'timing_mean_iat_{window}s',
                f'timing_var_iat_{window}s',
                f'timing_skew_iat_{window}s',
                f'timing_kurtosis_iat_{window}s'
            ])
        names = names[:24]  # Ensure exactly 24

        # Entropy features
        names.append('entropy_message_id')
        for i in range(8):
            names.append(f'entropy_payload_byte_{i}')
        for i in range(7):
            names.append(f'entropy_bit_{i}')

        # Signal-level features
        for i in range(5):
            names.extend([
                f'signal_{i}_mean',
                f'signal_{i}_var',
                f'signal_{i}_min',
                f'signal_{i}_max'
            ])

        # Contextual features
        for state in ['idle', 'starting', 'accelerating', 'cruising', 'braking']:
            names.append(f'context_state_{state}')
        names.extend([
            'context_trans_idle_starting',
            'context_trans_starting_accel',
            'context_trans_accel_cruise',
            'context_trans_cruise_brake',
            'context_trans_brake_idle'
        ])

        # Frequency features
        names.extend([
            'freq_dominant',
            'freq_centroid',
            'freq_spread',
            'freq_band_0_10Hz',
            'freq_band_10_20Hz',
            'freq_band_20_30Hz',
            'freq_band_30_40Hz',
            'freq_band_40_50Hz'
        ])

        return names[:78]  # Ensure exactly 78

    def reset(self):
        """Reset all extractors"""
        self.timing_extractor = TimingFeatureExtractor()
        self.entropy_extractor = EntropyFeatureExtractor()
        self.signal_extractor = SignalLevelFeatureExtractor()
        self.contextual_extractor = ContextualFeatureExtractor()
        self.frequency_extractor = FrequencyDomainFeatureExtractor()
        self.message_history.clear()