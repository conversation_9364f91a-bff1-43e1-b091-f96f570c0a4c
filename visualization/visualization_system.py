"""
Advanced Visualization System
Provides both human-interpretable visualizations and DRL-consumable metrics
"""
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import networkx as nx
from typing import Dict, List, Tuple, Optional, Any
from collections import defaultdict, deque
import time
from dataclasses import dataclass, field

from simulation.can_message import CANMessage, CANSignal

@dataclass
class VisualizationMetrics:
    """Metrics extracted from visualizations for DRL agent"""
    signal_type_entropy: float = 0.0
    type_clustering_coefficient: float = 0.0
    bit_width_variance: float = 0.0
    boundary_probabilities: List[float] = field(default_factory=list)
    graph_centrality_scores: Dict[str, float] = field(default_factory=dict)
    cluster_coefficients: List[float] = field(default_factory=list)
    temporal_anomaly_scores: List[float] = field(default_factory=list)
    response_sensitivity: float = 0.0
    bit_stability_scores: List[float] = field(default_factory=list)
    control_bit_indicators: List[int] = field(default_factory=list)

    def to_array(self) -> np.ndarray:
        """Convert to numpy array for DRL agent"""
        features = []

        # Add scalar features (4 features)
        features.append(self.signal_type_entropy)
        features.append(self.type_clustering_coefficient)
        features.append(self.bit_width_variance)
        features.append(self.response_sensitivity)

        # Add list features (truncate/pad to fixed size)
        # Boundary probabilities (3 features)
        if self.boundary_probabilities and len(self.boundary_probabilities) > 0:
            boundary_features = list(self.boundary_probabilities[:3])
            boundary_features.extend([0.0] * (3 - len(boundary_features)))
            features.extend(boundary_features)
        else:
            features.extend([0.0] * 3)

        # Cluster coefficients (3 features)
        if self.cluster_coefficients and len(self.cluster_coefficients) > 0:
            cluster_features = list(self.cluster_coefficients[:3])
            cluster_features.extend([0.0] * (3 - len(cluster_features)))
            features.extend(cluster_features)
        else:
            features.extend([0.0] * 3)

        # Temporal anomaly scores (2 features)
        if self.temporal_anomaly_scores and len(self.temporal_anomaly_scores) > 0:
            temporal_features = list(self.temporal_anomaly_scores[:2])
            temporal_features.extend([0.0] * (2 - len(temporal_features)))
            features.extend(temporal_features)
        else:
            features.extend([0.0] * 2)

        # Bit stability scores (3 features)
        if self.bit_stability_scores and len(self.bit_stability_scores) > 0:
            stability_features = list(self.bit_stability_scores[:3])
            stability_features.extend([0.0] * (3 - len(stability_features)))
            features.extend(stability_features)
        else:
            features.extend([0.0] * 3)

        # Ensure exactly 15 features
        if len(features) < 15:
            features.extend([0.0] * (15 - len(features)))

        return np.array(features[:15])  # Total 15 visualization features

class SignalTypeVisualizer:
    """Visualizes distribution of signal types across CAN network"""

    def __init__(self):
        self.signal_types = defaultdict(lambda: defaultdict(int))
        self.message_signal_map = {}

    def update(self, message: CANMessage):
        """Update with new message"""
        for signal_name, signal_def in message.signals.items():
            # Determine signal type
            signal_type = self._classify_signal_type(signal_def)
            self.signal_types[message.id][signal_type] += 1
            self.message_signal_map[f"{message.id:03X}_{signal_name}"] = signal_type

    def _classify_signal_type(self, signal: CANSignal) -> str:
        """Classify signal into type category"""
        if signal.length == 1:
            return 'boolean'
        elif signal.signed:
            return 'signed_int'
        elif signal.scale != 1.0 or signal.offset != 0.0:
            return 'physical'
        elif signal.max_value < 10:
            return 'enum'
        else:
            return 'unsigned_int'

    def visualize(self) -> go.Figure:
        """Create treemap visualization"""
        labels = []
        parents = []
        values = []
        colors = []

        type_colors = {
            'boolean': '#FF6B6B',
            'signed_int': '#4ECDC4',
            'unsigned_int': '#45B7D1',
            'physical': '#96CEB4',
            'enum': '#FECA57'
        }

        # Root
        labels.append("CAN Network")
        parents.append("")
        values.append(0)
        colors.append('#DDD')

        # Add message IDs and signal types
        for msg_id, type_counts in self.signal_types.items():
            msg_label = f"ID: 0x{msg_id:03X}"
            labels.append(msg_label)
            parents.append("CAN Network")
            values.append(sum(type_counts.values()))
            colors.append('#BBB')

            for sig_type, count in type_counts.items():
                labels.append(f"{sig_type} ({count})")
                parents.append(msg_label)
                values.append(count)
                colors.append(type_colors.get(sig_type, '#999'))

        fig = go.Figure(go.Treemap(
            labels=labels,
            parents=parents,
            values=values,
            marker=dict(colors=colors),
            textinfo="label+value+percent parent"
        ))

        fig.update_layout(
            title="Signal Type Distribution",
            height=600,
            margin=dict(l=0, r=0, t=40, b=0)
        )

        return fig

    def extract_metrics(self) -> Dict[str, float]:
        """Extract metrics for DRL agent"""
        # Calculate signal type entropy
        all_types = []
        for type_counts in self.signal_types.values():
            all_types.extend([t for t, c in type_counts.items() for _ in range(c)])

        if all_types:
            type_probs = {}
            for t in set(all_types):
                type_probs[t] = all_types.count(t) / len(all_types)

            entropy = -sum(p * np.log2(p) if p > 0 else 0 for p in type_probs.values())

            # Calculate clustering coefficient
            clustering = self._calculate_type_clustering()

            return {
                'signal_type_entropy': entropy,
                'type_clustering_coefficient': clustering
            }

        return {'signal_type_entropy': 0.0, 'type_clustering_coefficient': 0.0}

    def _calculate_type_clustering(self) -> float:
        """Calculate how signals of same type cluster together"""
        if not self.message_signal_map:
            return 0.0

        # Group signals by message
        message_types = defaultdict(list)
        for key, sig_type in self.message_signal_map.items():
            msg_id = key.split('_')[0]
            message_types[msg_id].append(sig_type)

        # Calculate clustering score
        clustering_scores = []
        for msg_id, types in message_types.items():
            if len(types) > 1:
                # Count same-type pairs
                same_type_pairs = sum(1 for i in range(len(types))
                                    for j in range(i+1, len(types))
                                    if types[i] == types[j])
                total_pairs = len(types) * (len(types) - 1) / 2
                clustering_scores.append(same_type_pairs / total_pairs if total_pairs > 0 else 0)

        return np.mean(clustering_scores) if clustering_scores else 0.0

class BitWidthVisualizer:
    """Visualizes bit width distribution to identify signal boundaries"""

    def __init__(self):
        self.bit_widths = defaultdict(list)
        self.message_layouts = {}

    def update(self, message: CANMessage):
        """Update with new message"""
        bit_layout = []
        for signal_name, signal_def in message.signals.items():
            self.bit_widths[message.id].append(signal_def.length)
            bit_layout.append({
                'name': signal_name,
                'start': signal_def.start_bit,
                'length': signal_def.length
            })
        self.message_layouts[message.id] = bit_layout

    def visualize(self) -> go.Figure:
        """Create bit width distribution visualization"""
        fig = make_subplots(
            rows=2, cols=1,
            subplot_titles=("Bit Width Histogram", "Message Bit Layouts"),
            row_heights=[0.4, 0.6],
            vertical_spacing=0.15
        )

        # Histogram of bit widths
        all_widths = []
        for widths in self.bit_widths.values():
            all_widths.extend(widths)

        if all_widths:
            fig.add_trace(
                go.Histogram(x=all_widths, nbinsx=20, name="Bit Widths"),
                row=1, col=1
            )

        # Heatmap of bit layouts
        if self.message_layouts:
            # Create bit usage matrix
            max_messages = min(20, len(self.message_layouts))
            bit_matrix = np.zeros((max_messages, 64))

            for idx, (msg_id, layout) in enumerate(list(self.message_layouts.items())[:max_messages]):
                for signal in layout:
                    start = signal['start']
                    length = signal['length']
                    bit_matrix[idx, start:start+length] = length

            fig.add_trace(
                go.Heatmap(
                    z=bit_matrix,
                    colorscale='Viridis',
                    showscale=True,
                    colorbar=dict(title="Bit Width"),
                    y=[f"0x{msg_id:03X}" for msg_id in list(self.message_layouts.keys())[:max_messages]],
                    x=list(range(64))
                ),
                row=2, col=1
            )

        fig.update_layout(
            title="Bit Width Distribution Analysis",
            height=800,
            showlegend=False
        )

        fig.update_xaxes(title_text="Bit Width", row=1, col=1)
        fig.update_xaxes(title_text="Bit Position", row=2, col=1)
        fig.update_yaxes(title_text="Count", row=1, col=1)
        fig.update_yaxes(title_text="Message ID", row=2, col=1)

        return fig

    def extract_metrics(self) -> Dict[str, Any]:
        """Extract metrics for DRL agent"""
        all_widths = []
        for widths in self.bit_widths.values():
            all_widths.extend(widths)

        if all_widths:
            # Calculate bit width variance
            variance = np.var(all_widths)

            # Calculate boundary probabilities
            boundary_probs = self._calculate_boundary_probabilities()

            return {
                'bit_width_variance': variance,
                'boundary_probabilities': boundary_probs
            }

        return {'bit_width_variance': 0.0, 'boundary_probabilities': [0.0] * 8}

    def _calculate_boundary_probabilities(self) -> List[float]:
        """Calculate probability of signal boundaries at each byte"""
        boundary_counts = [0] * 8
        total_signals = 0

        for layout in self.message_layouts.values():
            for signal in layout:
                start_byte = signal['start'] // 8
                end_byte = (signal['start'] + signal['length'] - 1) // 8

                if start_byte < 8:
                    boundary_counts[start_byte] += 1
                if end_byte < 8 and end_byte != start_byte:
                    boundary_counts[end_byte] += 1

                total_signals += 1

        if total_signals > 0:
            return [count / total_signals for count in boundary_counts]
        return [0.0] * 8

class CorrelationGraphVisualizer:
    """Visualizes signal correlations as a network graph"""

    def __init__(self, correlation_threshold: float = 0.7):
        self.correlation_threshold = correlation_threshold
        self.signal_values = defaultdict(deque)
        self.max_values = 1000
        self.graph = nx.Graph()

    def update(self, message: CANMessage):
        """Update with new message"""
        # Store signal values
        for signal_name in message.signals:
            value = message.get_signal_value(signal_name)
            if value is not None:
                key = f"{message.id:03X}_{signal_name}"
                if len(self.signal_values[key]) >= self.max_values:
                    self.signal_values[key].popleft()
                self.signal_values[key].append(value)

    def _calculate_correlations(self) -> Dict[Tuple[str, str], float]:
        """Calculate correlations between all signal pairs"""
        correlations = {}
        signal_keys = list(self.signal_values.keys())

        for i in range(len(signal_keys)):
            for j in range(i + 1, len(signal_keys)):
                key1, key2 = signal_keys[i], signal_keys[j]
                values1 = np.array(self.signal_values[key1])
                values2 = np.array(self.signal_values[key2])

                # Need same length and sufficient data
                min_len = min(len(values1), len(values2))
                if min_len > 10:
                    values1 = values1[-min_len:]
                    values2 = values2[-min_len:]

                    # Calculate correlation
                    if np.std(values1) > 0 and np.std(values2) > 0:
                        corr = np.corrcoef(values1, values2)[0, 1]
                        if abs(corr) > self.correlation_threshold:
                            correlations[(key1, key2)] = corr

        return correlations

    def visualize(self) -> go.Figure:
        """Create network graph visualization"""
        # Calculate correlations and build graph
        correlations = self._calculate_correlations()

        # Build graph
        self.graph.clear()
        for (node1, node2), corr in correlations.items():
            self.graph.add_edge(node1, node2, weight=abs(corr), correlation=corr)

        if len(self.graph.nodes) == 0:
            # Empty graph
            fig = go.Figure()
            fig.add_annotation(
                text="No significant correlations found",
                xref="paper", yref="paper",
                x=0.5, y=0.5,
                showarrow=False
            )
            fig.update_layout(title="Signal Correlation Graph")
            return fig

        # Calculate layout
        pos = nx.spring_layout(self.graph, k=2, iterations=50)

        # Create edge traces
        edge_traces = []
        for edge in self.graph.edges(data=True):
            x0, y0 = pos[edge[0]]
            x1, y1 = pos[edge[1]]
            corr = edge[2]['correlation']

            edge_trace = go.Scatter(
                x=[x0, x1, None],
                y=[y0, y1, None],
                mode='lines',
                line=dict(
                    width=abs(corr) * 5,
                    color='red' if corr < 0 else 'green'
                ),
                hoverinfo='text',
                text=f"Correlation: {corr:.3f}",
                showlegend=False
            )
            edge_traces.append(edge_trace)

        # Create node trace
        node_x = []
        node_y = []
        node_text = []
        node_color = []

        for node in self.graph.nodes():
            x, y = pos[node]
            node_x.append(x)
            node_y.append(y)
            node_text.append(node)
            # Color by message ID
            msg_id = int(node.split('_')[0], 16)
            node_color.append(msg_id)

        node_trace = go.Scatter(
            x=node_x,
            y=node_y,
            mode='markers+text',
            text=node_text,
            textposition='top center',
            marker=dict(
                size=20,
                color=node_color,
                colorscale='Viridis',
                showscale=True,
                colorbar=dict(title="Message ID")
            ),
            hoverinfo='text',
            showlegend=False
        )

        # Create figure
        fig = go.Figure(data=edge_traces + [node_trace])

        fig.update_layout(
            title="Signal Correlation Network",
            showlegend=False,
            hovermode='closest',
            margin=dict(b=0, l=0, r=0, t=40),
            xaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
            yaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
            height=600
        )

        return fig

    def extract_metrics(self) -> Dict[str, Any]:
        """Extract graph metrics for DRL agent"""
        if len(self.graph.nodes) == 0:
            return {
                'graph_centrality_scores': {},
                'cluster_coefficients': [0.0]
            }

        # Calculate centrality measures
        centrality = nx.degree_centrality(self.graph)
        top_central = sorted(centrality.items(), key=lambda x: x[1], reverse=True)[:5]

        # Calculate clustering coefficients
        clustering = nx.clustering(self.graph)
        cluster_values = list(clustering.values())

        return {
            'graph_centrality_scores': dict(top_central),
            'cluster_coefficients': cluster_values[:10]
        }

class TemporalSignalVisualizer:
    """Visualizes signal values over time with anomaly detection"""

    def __init__(self, window_size: int = 1000):
        self.window_size = window_size
        self.signal_history = defaultdict(lambda: {
            'timestamps': deque(maxlen=window_size),
            'values': deque(maxlen=window_size)
        })
        self.anomaly_scores = defaultdict(list)
        self.baseline_stats = {}

    def update(self, message: CANMessage):
        """Update with new message"""
        timestamp = message.timestamp

        for signal_name in message.signals:
            value = message.get_signal_value(signal_name)
            if value is not None:
                key = f"{message.id:03X}_{signal_name}"
                self.signal_history[key]['timestamps'].append(timestamp)
                self.signal_history[key]['values'].append(value)

                # Calculate anomaly score
                anomaly_score = self._calculate_anomaly_score(key, value)
                if anomaly_score > 0:
                    self.anomaly_scores[key].append({
                        'timestamp': timestamp,
                        'score': anomaly_score,
                        'value': value
                    })

    def _calculate_anomaly_score(self, signal_key: str, value: float) -> float:
        """Calculate anomaly score for a signal value"""
        values = list(self.signal_history[signal_key]['values'])

        if len(values) < 50:
            return 0.0

        # Update baseline statistics
        if signal_key not in self.baseline_stats or len(values) % 100 == 0:
            self.baseline_stats[signal_key] = {
                'mean': np.mean(values[:-1]),
                'std': np.std(values[:-1])
            }

        stats = self.baseline_stats[signal_key]
        if stats['std'] > 0:
            z_score = abs((value - stats['mean']) / stats['std'])
            return z_score if z_score > 3 else 0.0

        return 0.0

    def visualize(self, signals_to_plot: Optional[List[str]] = None) -> go.Figure:
        """Create temporal visualization with anomaly highlighting"""
        if signals_to_plot is None:
            # Select most active signals
            signals_to_plot = sorted(
                self.signal_history.keys(),
                key=lambda k: len(self.signal_history[k]['values']),
                reverse=True
            )[:4]

        rows = len(signals_to_plot)
        if rows == 0:
            fig = go.Figure()
            fig.add_annotation(
                text="No signals to display",
                xref="paper", yref="paper",
                x=0.5, y=0.5,
                showarrow=False
            )
            fig.update_layout(title="Temporal Signal Analysis")
            return fig

        fig = make_subplots(
            rows=rows, cols=1,
            subplot_titles=signals_to_plot,
            shared_xaxes=True,
            vertical_spacing=0.05
        )

        for idx, signal_key in enumerate(signals_to_plot):
            if signal_key in self.signal_history:
                data = self.signal_history[signal_key]

                # Main signal trace
                fig.add_trace(
                    go.Scatter(
                        x=list(data['timestamps']),
                        y=list(data['values']),
                        mode='lines',
                        name=signal_key,
                        line=dict(color='blue', width=1)
                    ),
                    row=idx+1, col=1
                )

                # Add anomalies
                if signal_key in self.anomaly_scores:
                    anomalies = self.anomaly_scores[signal_key][-20:]  # Last 20 anomalies
                    if anomalies:
                        fig.add_trace(
                            go.Scatter(
                                x=[a['timestamp'] for a in anomalies],
                                y=[a['value'] for a in anomalies],
                                mode='markers',
                                name=f"{signal_key} anomalies",
                                marker=dict(
                                    color='red',
                                    size=[min(a['score'] * 3, 15) for a in anomalies],
                                    symbol='circle-open'
                                )
                            ),
                            row=idx+1, col=1
                        )

        fig.update_layout(
            title="Temporal Signal Analysis with Anomaly Detection",
            height=200 * rows,
            showlegend=False
        )

        fig.update_xaxes(title_text="Time", row=rows, col=1)

        return fig

    def extract_metrics(self) -> Dict[str, Any]:
        """Extract temporal metrics for DRL agent"""
        # Calculate overall anomaly scores
        all_anomaly_scores = []
        response_sensitivities = []

        for signal_key, anomalies in self.anomaly_scores.items():
            if anomalies:
                scores = [a['score'] for a in anomalies]
                all_anomaly_scores.extend(scores)

                # Calculate response sensitivity (variance in values after anomalies)
                values = list(self.signal_history[signal_key]['values'])
                if len(values) > 10:
                    response_sensitivities.append(np.var(values[-10:]))

        return {
            'temporal_anomaly_scores': all_anomaly_scores[:10],
            'response_sensitivity': np.mean(response_sensitivities) if response_sensitivities else 0.0
        }

class HAMDVisualizer:
    """Hamming Distance visualizer for bit-level analysis"""

    def __init__(self):
        self.bit_flip_counts = defaultdict(lambda: np.zeros(64))
        self.message_count = defaultdict(int)
        self.last_messages = {}

    def update(self, message: CANMessage):
        """Update with new message"""
        msg_id = message.id

        if msg_id in self.last_messages:
            # Calculate bit differences
            last_data = self.last_messages[msg_id]
            current_data = message.data

            for byte_idx in range(min(len(last_data), len(current_data))):
                diff = last_data[byte_idx] ^ current_data[byte_idx]
                for bit_idx in range(8):
                    if diff & (1 << bit_idx):
                        self.bit_flip_counts[msg_id][byte_idx * 8 + bit_idx] += 1

        self.last_messages[msg_id] = message.data
        self.message_count[msg_id] += 1

    def visualize(self) -> go.Figure:
        """Create bit flip heatmap visualization"""
        # Select messages with most data
        messages_to_plot = sorted(
            self.message_count.keys(),
            key=lambda k: self.message_count[k],
            reverse=True
        )[:10]

        if not messages_to_plot:
            fig = go.Figure()
            fig.add_annotation(
                text="No bit flip data available",
                xref="paper", yref="paper",
                x=0.5, y=0.5,
                showarrow=False
            )
            fig.update_layout(title="Hamming Distance Analysis")
            return fig

        # Create flip rate matrix
        flip_rate_matrix = []
        labels = []

        for msg_id in messages_to_plot:
            if self.message_count[msg_id] > 1:
                flip_rates = self.bit_flip_counts[msg_id] / (self.message_count[msg_id] - 1)
                flip_rate_matrix.append(flip_rates[:64])
                labels.append(f"0x{msg_id:03X}")

        if flip_rate_matrix:
            flip_rate_matrix = np.array(flip_rate_matrix)

            # Create heatmap
            fig = go.Figure(data=go.Heatmap(
                z=flip_rate_matrix,
                x=[f"Bit {i}" for i in range(64)],
                y=labels,
                colorscale='Hot',
                colorbar=dict(title="Flip Rate"),
                hoverongaps=False
            ))

            # Add bit position markers
            for i in range(0, 64, 8):
                fig.add_vline(x=i-0.5, line_width=2, line_color="white", opacity=0.5)

            fig.update_layout(
                title="Bit Flip Rate Analysis (Hamming Distance)",
                xaxis_title="Bit Position",
                yaxis_title="Message ID",
                height=400
            )
        else:
            fig = go.Figure()

        return fig

    def extract_metrics(self) -> Dict[str, Any]:
        """Extract bit-level metrics for DRL agent"""
        all_stability_scores = []
        control_bit_indicators = []

        for msg_id, flip_counts in self.bit_flip_counts.items():
            if self.message_count[msg_id] > 1:
                flip_rates = flip_counts / (self.message_count[msg_id] - 1)

                # Bit stability scores (inverse of flip rate)
                stability = 1 - flip_rates
                all_stability_scores.extend(stability[:8])  # First byte

                # Identify potential control bits (very stable bits)
                control_bits = np.where(stability > 0.95)[0]
                control_bit_indicators.extend(control_bits[:5])

        return {
            'bit_stability_scores': all_stability_scores[:10],
            'control_bit_indicators': control_bit_indicators[:5]
        }

class VisualizationSystem:
    """Main visualization system integrating all modules"""

    def __init__(self, config=None):
        self.config = config or {}

        # Initialize visualizers
        self.signal_type_viz = SignalTypeVisualizer()
        self.bit_width_viz = BitWidthVisualizer()
        self.correlation_viz = CorrelationGraphVisualizer()
        self.temporal_viz = TemporalSignalVisualizer()
        self.hamd_viz = HAMDVisualizer()

        # Metrics cache
        self.last_metrics = VisualizationMetrics()
        self.update_counter = 0

    def update(self, message: CANMessage):
        """Update all visualizers with new message"""
        self.signal_type_viz.update(message)
        self.bit_width_viz.update(message)
        self.correlation_viz.update(message)
        self.temporal_viz.update(message)
        self.hamd_viz.update(message)

        self.update_counter += 1

        # Update metrics periodically
        if self.update_counter % 100 == 0:
            self._update_metrics()

    def _update_metrics(self):
        """Update cached metrics"""
        # Extract metrics from each visualizer
        signal_type_metrics = self.signal_type_viz.extract_metrics()
        bit_width_metrics = self.bit_width_viz.extract_metrics()
        correlation_metrics = self.correlation_viz.extract_metrics()
        temporal_metrics = self.temporal_viz.extract_metrics()
        hamd_metrics = self.hamd_viz.extract_metrics()

        # Update metrics object
        self.last_metrics.signal_type_entropy = signal_type_metrics['signal_type_entropy']
        self.last_metrics.type_clustering_coefficient = signal_type_metrics['type_clustering_coefficient']
        self.last_metrics.bit_width_variance = bit_width_metrics['bit_width_variance']
        self.last_metrics.boundary_probabilities = bit_width_metrics['boundary_probabilities']
        self.last_metrics.graph_centrality_scores = correlation_metrics['graph_centrality_scores']
        self.last_metrics.cluster_coefficients = correlation_metrics['cluster_coefficients']
        self.last_metrics.temporal_anomaly_scores = temporal_metrics['temporal_anomaly_scores']
        self.last_metrics.response_sensitivity = temporal_metrics['response_sensitivity']
        self.last_metrics.bit_stability_scores = hamd_metrics['bit_stability_scores']
        self.last_metrics.control_bit_indicators = hamd_metrics['control_bit_indicators']

    def get_metrics(self) -> VisualizationMetrics:
        """Get current visualization metrics for DRL agent"""
        return self.last_metrics

    def create_dashboard(self) -> go.Figure:
        """Create comprehensive dashboard with all visualizations"""
        from plotly.subplots import make_subplots

        fig = make_subplots(
            rows=3, cols=2,
            subplot_titles=(
                "Signal Type Distribution",
                "Bit Width Analysis",
                "Correlation Network",
                "Temporal Signals",
                "Hamming Distance",
                "Metrics Summary"
            ),
            specs=[
                [{"type": "treemap"}, {"type": "xy"}],
                [{"type": "xy"}, {"type": "xy"}],
                [{"type": "xy"}, {"type": "table"}]
            ],
            row_heights=[0.3, 0.35, 0.35],
            horizontal_spacing=0.1,
            vertical_spacing=0.1
        )

        # Note: Full dashboard implementation would require custom subplot handling
        # This is a simplified version

        fig.update_layout(
            title="AMCFF-RL Visualization Dashboard",
            height=1200,
            showlegend=False
        )

        return fig

    def save_visualizations(self, output_dir: str):
        """Save all visualizations to files"""
        import os

        os.makedirs(output_dir, exist_ok=True)

        # Save individual visualizations
        figs = {
            'signal_types': self.signal_type_viz.visualize(),
            'bit_widths': self.bit_width_viz.visualize(),
            'correlations': self.correlation_viz.visualize(),
            'temporal': self.temporal_viz.visualize(),
            'hamd': self.hamd_viz.visualize()
        }

        for name, fig in figs.items():
            fig.write_html(os.path.join(output_dir, f"{name}.html"))
            fig.write_image(os.path.join(output_dir, f"{name}.png"))

    def reset(self):
        """Reset all visualizers"""
        self.signal_type_viz = SignalTypeVisualizer()
        self.bit_width_viz = BitWidthVisualizer()
        self.correlation_viz = CorrelationGraphVisualizer()
        self.temporal_viz = TemporalSignalVisualizer()
        self.hamd_viz = HAMDVisualizer()
        self.last_metrics = VisualizationMetrics()
        self.update_counter = 0