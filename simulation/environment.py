"""
Main CAN Bus Simulation Environment
Integrates all simulation components and provides gym-like interface
"""
import time
import random
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
from collections import deque
from dataclasses import dataclass
import logging

from .can_message import CANMessage, can_db
from .ecu_emulator import ECUManager, ECUState
from .network_simulator import CANNetworkSimulator, NetworkAnomalyDetector

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class VehicleState:
    """Represents the current state of the vehicle"""
    speed: float = 0.0
    rpm: float = 0.0
    steering_angle: float = 0.0
    brake_pressure: float = 0.0
    throttle_position: float = 0.0
    gear: int = 0
    operational_mode: str = 'idle'  # idle, starting, driving, braking

    def to_vector(self) -> np.ndarray:
        """Convert to numpy vector"""
        mode_map = {'idle': 0, 'starting': 1, 'driving': 2, 'braking': 3}
        return np.array([
            self.speed,
            self.rpm,
            self.steering_angle,
            self.brake_pressure,
            self.throttle_position,
            self.gear,
            mode_map.get(self.operational_mode, 0)
        ])

class CANEnvironment:
    """Main CAN Bus Simulation Environment"""

    def __init__(self, config=None):
        """Initialize the CAN environment"""
        self.config = config or {}

        # Core components
        self.ecu_manager = ECUManager()
        self.network = CANNetworkSimulator()
        self.anomaly_detector = NetworkAnomalyDetector()

        # Vehicle state
        self.vehicle_state = VehicleState()
        self.simulation_time = 0.0
        self.timestep = self.config.get('timestep', 0.001)  # 1ms

        # Message tracking
        self.message_buffer = deque(maxlen=1000)
        self.injected_messages = []
        self.detected_anomalies = []

        # Action tracking
        self.last_action = None
        self.action_history = deque(maxlen=100)

        # Safety mechanisms
        self.safety_violations = 0
        self.safety_threshold = self.config.get('safety_threshold', 10)

        # Setup callbacks
        self.network.add_message_callback(self._on_message_received)
        self.network.add_error_callback(self._on_network_error)

        # Initialize environment
        self.reset()

    def reset(self) -> Dict[str, Any]:
        """Reset the environment to initial state"""
        logger.info("Resetting CAN environment")

        # Reset all components
        self.ecu_manager.power_off_all()
        self.network.reset()
        self.anomaly_detector = NetworkAnomalyDetector()

        # Reset state
        self.vehicle_state = VehicleState()
        self.simulation_time = 0.0
        self.message_buffer.clear()
        self.injected_messages.clear()
        self.detected_anomalies.clear()
        self.action_history.clear()
        self.safety_violations = 0

        # Power on ECUs
        self.ecu_manager.power_on_all()

        # Run initial steps to stabilize
        for _ in range(100):
            self._simulation_step()

        return self._get_observation()

    def step(self, action: Dict[str, Any]) -> Tuple[Dict[str, Any], float, bool, Dict[str, Any]]:
        """Execute an action and return observation, reward, done, info"""
        # Validate and apply action
        if self._validate_action(action):
            self._apply_action(action)
            self.last_action = action
            self.action_history.append(action)
        else:
            logger.warning(f"Invalid action rejected: {action}")
            self.safety_violations += 1

        # Run simulation steps
        messages_transmitted = []
        for _ in range(10):  # 10ms worth of simulation
            msgs = self._simulation_step()
            messages_transmitted.extend(msgs)

        # Get observation
        observation = self._get_observation()

        # Calculate reward
        reward = self._calculate_reward(action, messages_transmitted)

        # Check termination conditions
        done = self._check_termination()

        # Additional info
        info = {
            'messages_transmitted': messages_transmitted,
            'detected_anomalies': list(self.detected_anomalies),
            'safety_violations': self.safety_violations,
            'network_stats': self.network.get_statistics(),
            'ecu_states': self.ecu_manager.get_ecu_states()
        }

        return observation, reward, done, info

    def _simulation_step(self) -> List[CANMessage]:
        """Run one simulation timestep"""
        # Update ECUs
        ecu_messages = self.ecu_manager.update_all(self.simulation_time)

        # Transmit ECU messages
        for msg in ecu_messages:
            self.network.transmit(msg)

        # Step network simulation
        transmitted = self.network.step(self.timestep)

        # Distribute messages back to ECUs
        for msg in transmitted:
            self.ecu_manager.distribute_message(msg)

        # Update vehicle state based on messages
        self._update_vehicle_state(transmitted)

        # Update time
        self.simulation_time += self.timestep

        return transmitted

    def _validate_action(self, action: Dict[str, Any]) -> bool:
        """Validate an action for safety"""
        action_type = action.get('type')
        target_id = action.get('target_id')

        # Check blacklist
        if self.config.get('safety_enabled', True):
            blacklist = self.config.get('dangerous_operations', [])
            for forbidden in blacklist:
                if (forbidden[0] == f"0x{target_id:X}" and
                    forbidden[1] == action_type):
                    return False

        # Check safety critical messages
        safety_critical = [0x100, 0x101, 0x102]  # Engine, Brake, Steering
        if target_id in safety_critical:
            # Additional validation for critical systems
            if action_type == 'value_manipulation':
                # Check value ranges
                values = action.get('values', {})
                for signal, value in values.items():
                    if not self._validate_signal_value(target_id, signal, value):
                        return False

        return True

    def _validate_signal_value(self, msg_id: int, signal_name: str, value: float) -> bool:
        """Validate a signal value is within safe bounds"""
        # Get signal definition
        msg_def = can_db.get_message_definition(msg_id)
        if not msg_def or signal_name not in msg_def['signals']:
            return False

        signal = msg_def['signals'][signal_name]

        # Check basic bounds
        if value < signal.min_value or value > signal.max_value:
            return False

        # Additional safety checks
        if msg_id == 0x101 and signal_name == 'BRAKE_PRESSURE':
            # Prevent instant max braking
            current_pressure = self.vehicle_state.brake_pressure
            if value > current_pressure + 50:  # Max 50 bar increase
                return False

        return True

    def _apply_action(self, action: Dict[str, Any]):
        """Apply a fuzzing action"""
        action_type = action.get('type')
        target_id = action.get('target_id')

        if action_type == 'value_manipulation':
            self._apply_value_manipulation(target_id, action.get('values', {}))

        elif action_type == 'bit_flipping':
            self._apply_bit_flipping(target_id, action.get('bit_positions', []))

        elif action_type == 'timing_modification':
            self._apply_timing_modification(target_id, action.get('delay', 0))

        elif action_type == 'message_injection':
            self._inject_message(target_id, action.get('data', bytes(8)))

        elif action_type == 'sequence_manipulation':
            self._apply_sequence_manipulation(action.get('sequence', []))

    def _apply_value_manipulation(self, msg_id: int, values: Dict[str, float]):
        """Manipulate signal values in a message"""
        message = can_db.create_message(msg_id, values)
        message.timestamp = self.simulation_time
        self.network.inject_message(message)
        self.injected_messages.append(message)

    def _apply_bit_flipping(self, msg_id: int, bit_positions: List[int]):
        """Flip specific bits in a message"""
        # Get last message of this ID
        last_msg = None
        for msg in reversed(self.message_buffer):
            if msg.id == msg_id:
                last_msg = msg
                break

        if last_msg:
            # Clone and flip bits
            new_msg = last_msg.clone()
            data_array = bytearray(new_msg.data)

            for bit_pos in bit_positions:
                if 0 <= bit_pos < len(data_array) * 8:
                    byte_idx = bit_pos // 8
                    bit_idx = bit_pos % 8
                    data_array[byte_idx] ^= (1 << bit_idx)

            new_msg.data = bytes(data_array)
            new_msg.timestamp = self.simulation_time
            self.network.inject_message(new_msg)
            self.injected_messages.append(new_msg)

    def _apply_timing_modification(self, msg_id: int, delay: float):
        """Modify timing of a specific message"""
        # This would need to be implemented in the ECU to be effective
        # For now, we'll inject a delayed version
        last_msg = None
        for msg in reversed(self.message_buffer):
            if msg.id == msg_id:
                last_msg = msg
                break

        if last_msg:
            delayed_msg = last_msg.clone()
            delayed_msg.timestamp = self.simulation_time + delay
            # Schedule for future injection
            # This is simplified - in reality would need event scheduling
            self.network.inject_message(delayed_msg)
            self.injected_messages.append(delayed_msg)

    def _inject_message(self, msg_id: int, data: bytes):
        """Inject a raw message"""
        message = CANMessage(id=msg_id, data=data, timestamp=self.simulation_time)
        self.network.inject_message(message)
        self.injected_messages.append(message)

    def _apply_sequence_manipulation(self, sequence: List[int]):
        """Inject a sequence of messages"""
        for msg_id in sequence:
            # Get last message of this ID and re-inject
            for msg in reversed(self.message_buffer):
                if msg.id == msg_id:
                    new_msg = msg.clone()
                    new_msg.timestamp = self.simulation_time
                    self.network.inject_message(new_msg)
                    self.injected_messages.append(new_msg)
                    break

    def _on_message_received(self, message: CANMessage):
        """Callback for received messages"""
        self.message_buffer.append(message)

        # Check for anomalies
        anomaly = self.anomaly_detector.update(message)
        if anomaly:
            self.detected_anomalies.append({
                'type': anomaly,
                'message_id': message.id,
                'timestamp': message.timestamp
            })

    def _on_network_error(self, error_type: str, details: Any):
        """Callback for network errors"""
        logger.error(f"Network error: {error_type}, details: {details}")
        self.detected_anomalies.append({
            'type': f'network_{error_type}',
            'details': details,
            'timestamp': self.simulation_time
        })

    def _update_vehicle_state(self, messages: List[CANMessage]):
        """Update vehicle state based on CAN messages"""
        for msg in messages:
            if msg.id == 0x100:  # Engine data
                rpm = msg.get_signal_value('ENGINE_RPM')
                throttle = msg.get_signal_value('THROTTLE_POS')
                if rpm is not None:
                    self.vehicle_state.rpm = rpm
                if throttle is not None:
                    self.vehicle_state.throttle_position = throttle

            elif msg.id == 0x101:  # Brake data
                pressure = msg.get_signal_value('BRAKE_PRESSURE')
                if pressure is not None:
                    self.vehicle_state.brake_pressure = pressure

            elif msg.id == 0x102:  # Steering data
                angle = msg.get_signal_value('STEERING_ANGLE')
                if angle is not None:
                    self.vehicle_state.steering_angle = angle

            elif msg.id == 0x103:  # Vehicle speed
                # Average of wheel speeds
                speeds = []
                for wheel in ['FL', 'FR', 'RL', 'RR']:
                    speed = msg.get_signal_value(f'WHEEL_SPEED_{wheel}')
                    if speed is not None:
                        speeds.append(speed)
                if speeds:
                    self.vehicle_state.speed = np.mean(speeds)

        # Update operational mode
        self._update_operational_mode()

    def _update_operational_mode(self):
        """Update vehicle operational mode based on state"""
        if self.vehicle_state.rpm == 0:
            self.vehicle_state.operational_mode = 'idle'
        elif self.vehicle_state.rpm > 0 and self.vehicle_state.speed < 5:
            self.vehicle_state.operational_mode = 'starting'
        elif self.vehicle_state.brake_pressure > 10:
            self.vehicle_state.operational_mode = 'braking'
        else:
            self.vehicle_state.operational_mode = 'driving'

    def _get_observation(self) -> Dict[str, Any]:
        """Get current observation"""
        # Recent messages
        recent_messages = list(self.message_buffer)[-100:]

        # Network statistics
        network_stats = self.network.get_statistics()

        # ECU states
        ecu_states = self.ecu_manager.get_ecu_states()

        return {
            'vehicle_state': self.vehicle_state.to_vector(),
            'recent_messages': recent_messages,
            'network_stats': network_stats,
            'ecu_states': ecu_states,
            'detected_anomalies': len(self.detected_anomalies),
            'simulation_time': self.simulation_time
        }

    def _calculate_reward(self, action: Dict[str, Any], messages: List[CANMessage]) -> float:
        """Calculate reward for the action"""
        reward = 0.0

        # Reward for discovering anomalies
        new_anomalies = len(self.detected_anomalies)
        if new_anomalies > 0:
            reward += new_anomalies * 10.0

        # Reward for causing state changes
        # This is simplified - in practice would be more sophisticated
        if len(self.injected_messages) > 0:
            reward += 1.0

        # Penalty for safety violations
        if self.safety_violations > 0:
            reward -= self.safety_violations * 5.0

        # Penalty for repeated actions
        if action in self.action_history:
            reward -= 0.5

        return reward

    def _check_termination(self) -> bool:
        """Check if episode should terminate"""
        # Too many safety violations
        if self.safety_violations >= self.safety_threshold:
            logger.warning("Terminating due to safety violations")
            return True

        # Time limit
        if self.simulation_time > 60.0:  # 60 seconds
            return True

        # Critical system failure
        ecu_states = self.ecu_manager.get_ecu_states()
        critical_ecus = [1, 2, 3]  # Engine, Brake, Steering
        for ecu_id in critical_ecus:
            if ecu_id in ecu_states and ecu_states[ecu_id] == ECUState.ERROR:
                logger.warning(f"Critical ECU {ecu_id} in error state")
                return True

        return False

    def get_action_space(self) -> Dict[str, Any]:
        """Get the action space specification"""
        return {
            'type': ['value_manipulation', 'bit_flipping', 'timing_modification',
                    'message_injection', 'sequence_manipulation'],
            'target_id': list(range(0x100, 0x200)),
            'parameters': {
                'values': 'dict of signal:value pairs',
                'bit_positions': 'list of bit positions',
                'delay': 'float seconds',
                'data': 'bytes',
                'sequence': 'list of message IDs'
            }
        }

    def render(self) -> str:
        """Render current state as string"""
        output = []
        output.append(f"=== CAN Environment State ===")
        output.append(f"Time: {self.simulation_time:.3f}s")
        output.append(f"Vehicle: {self.vehicle_state.operational_mode}")
        output.append(f"  Speed: {self.vehicle_state.speed:.1f} km/h")
        output.append(f"  RPM: {self.vehicle_state.rpm:.0f}")
        output.append(f"  Steering: {self.vehicle_state.steering_angle:.1f}°")
        output.append(f"  Brake: {self.vehicle_state.brake_pressure:.1f} bar")
        output.append(f"Network:")
        stats = self.network.get_statistics()
        output.append(f"  Messages: {stats['messages_sent']}")
        output.append(f"  Errors: {stats['bus_errors']}")
        output.append(f"  Utilization: {stats['bus_utilization']:.1f}%")
        output.append(f"Anomalies: {len(self.detected_anomalies)}")
        output.append(f"Safety Violations: {self.safety_violations}")

        return '\n'.join(output)