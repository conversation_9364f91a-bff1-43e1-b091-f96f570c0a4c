"""
Prioritized Experience Replay Buffer
Implements prioritized sampling for more efficient learning
"""
import numpy as np
import torch
from typing import Tuple, List, Optional
from collections import namedtuple
import random

# Experience tuple
Experience = namedtuple('Experience',
    ['state', 'action', 'reward', 'next_state', 'done'])

class SumTree:
    """Sum tree data structure for efficient prioritized sampling"""

    def __init__(self, capacity: int):
        self.capacity = capacity
        self.tree = np.zeros(2 * capacity - 1)
        self.data = np.zeros(capacity, dtype=object)
        self.write_idx = 0
        self.n_entries = 0

    def _propagate(self, idx: int, change: float):
        """Propagate priority change up the tree"""
        parent = (idx - 1) // 2
        self.tree[parent] += change
        if parent != 0:
            self._propagate(parent, change)

    def _retrieve(self, idx: int, s: float) -> int:
        """Retrieve sample index based on priority sum"""
        left = 2 * idx + 1
        right = left + 1

        if left >= len(self.tree):
            return idx

        if s <= self.tree[left]:
            return self._retrieve(left, s)
        else:
            return self._retrieve(right, s - self.tree[left])

    def total(self) -> float:
        """Get total priority sum"""
        return self.tree[0]

    def add(self, priority: float, data: Experience):
        """Add new experience with priority"""
        idx = self.write_idx + self.capacity - 1

        self.data[self.write_idx] = data
        self.update(idx, priority)

        self.write_idx += 1
        if self.write_idx >= self.capacity:
            self.write_idx = 0

        if self.n_entries < self.capacity:
            self.n_entries += 1

    def update(self, idx: int, priority: float):
        """Update priority of existing experience"""
        change = priority - self.tree[idx]
        self.tree[idx] = priority
        self._propagate(idx, change)

    def get(self, s: float) -> Tuple[int, float, Experience]:
        """Get experience based on priority sum"""
        idx = self._retrieve(0, s)
        data_idx = idx - self.capacity + 1

        # Ensure we only sample from valid entries
        if data_idx < 0 or data_idx >= self.n_entries:
            data_idx = np.random.randint(0, self.n_entries)
            idx = data_idx + self.capacity - 1

        return idx, self.tree[idx], self.data[data_idx]

class PrioritizedReplayBuffer:
    """Prioritized Experience Replay Buffer"""

    def __init__(self,
                 capacity: int,
                 alpha: float = 0.6,
                 beta_start: float = 0.4,
                 beta_frames: int = 100000):
        """
        Initialize prioritized replay buffer

        Args:
            capacity: Maximum buffer size
            alpha: Priority exponent (0 = uniform, 1 = full prioritization)
            beta_start: Initial importance sampling weight
            beta_frames: Frames over which to anneal beta to 1.0
        """
        self.capacity = capacity
        self.alpha = alpha
        self.beta_start = beta_start
        self.beta_frames = beta_frames
        self.frame = 1

        self.tree = SumTree(capacity)
        self.max_priority = 1.0

    def beta(self) -> float:
        """Calculate current beta value"""
        return min(1.0, self.beta_start + self.frame * (1.0 - self.beta_start) / self.beta_frames)

    def push(self, state: np.ndarray, action: int, reward: float,
             next_state: np.ndarray, done: bool):
        """Add experience to buffer"""
        experience = Experience(state, action, reward, next_state, done)

        # New experiences get max priority
        priority = self.max_priority ** self.alpha
        self.tree.add(priority, experience)

        self.frame += 1

    def sample(self, batch_size: int) -> Tuple[List[Experience], np.ndarray, List[int]]:
        """Sample batch of experiences with importance weights"""
        experiences = []
        idxs = []
        priorities = []
        segment = self.tree.total() / batch_size

        for i in range(batch_size):
            a = segment * i
            b = segment * (i + 1)

            s = random.uniform(a, b)
            idx, priority, experience = self.tree.get(s)

            experiences.append(experience)
            idxs.append(idx)
            priorities.append(priority)

        # Calculate importance sampling weights
        probs = np.array(priorities) / self.tree.total()
        weights = (self.tree.n_entries * probs) ** (-self.beta())
        weights = weights / weights.max()  # Normalize

        return experiences, weights, idxs

    def update_priorities(self, idxs: List[int], priorities: np.ndarray):
        """Update priorities based on TD errors"""
        for idx, priority in zip(idxs, priorities):
            priority = (priority + 1e-6) ** self.alpha  # Add small epsilon
            self.max_priority = max(self.max_priority, priority)
            self.tree.update(idx, priority)

    def __len__(self) -> int:
        """Get current buffer size"""
        return self.tree.n_entries

class SimpleReplayBuffer:
    """Simple uniform replay buffer for comparison"""

    def __init__(self, capacity: int):
        self.capacity = capacity
        self.buffer = []
        self.position = 0

    def push(self, state: np.ndarray, action: int, reward: float,
             next_state: np.ndarray, done: bool):
        """Add experience to buffer"""
        if len(self.buffer) < self.capacity:
            self.buffer.append(None)

        self.buffer[self.position] = Experience(state, action, reward, next_state, done)
        self.position = (self.position + 1) % self.capacity

    def sample(self, batch_size: int) -> List[Experience]:
        """Sample batch uniformly"""
        return random.sample(self.buffer, batch_size)

    def __len__(self) -> int:
        """Get current buffer size"""
        return len(self.buffer)

class MultiStepReplayBuffer:
    """Multi-step replay buffer for n-step returns"""

    def __init__(self,
                 capacity: int,
                 n_steps: int = 3,
                 gamma: float = 0.99,
                 use_prioritized: bool = True,
                 alpha: float = 0.6,
                 beta_start: float = 0.4):
        self.n_steps = n_steps
        self.gamma = gamma
        self.n_step_buffer = []

        if use_prioritized:
            self.buffer = PrioritizedReplayBuffer(capacity, alpha, beta_start)
        else:
            self.buffer = SimpleReplayBuffer(capacity)

    def push(self, state: np.ndarray, action: int, reward: float,
             next_state: np.ndarray, done: bool):
        """Add experience to n-step buffer"""
        self.n_step_buffer.append(Experience(state, action, reward, next_state, done))

        # Process n-step returns
        if len(self.n_step_buffer) >= self.n_steps or done:
            self._add_n_step_experience()

        if done:
            # Add remaining experiences
            while self.n_step_buffer:
                self._add_n_step_experience()

    def _add_n_step_experience(self):
        """Calculate n-step return and add to main buffer"""
        if not self.n_step_buffer:
            return

        # Calculate n-step return
        n_step_return = 0
        for i, exp in enumerate(self.n_step_buffer):
            n_step_return += (self.gamma ** i) * exp.reward

        # Get first and last states
        first_exp = self.n_step_buffer[0]
        last_exp = self.n_step_buffer[-1]

        # Add to main buffer
        self.buffer.push(
            first_exp.state,
            first_exp.action,
            n_step_return,
            last_exp.next_state,
            last_exp.done
        )

        # Remove first experience
        self.n_step_buffer.pop(0)

    def sample(self, batch_size: int):
        """Sample from buffer"""
        return self.buffer.sample(batch_size)

    def update_priorities(self, idxs: List[int], priorities: np.ndarray):
        """Update priorities if using prioritized replay"""
        if hasattr(self.buffer, 'update_priorities'):
            self.buffer.update_priorities(idxs, priorities)

    def __len__(self) -> int:
        """Get current buffer size"""
        return len(self.buffer)

def experience_to_tensors(experiences: List[Experience], device: torch.device) -> Tuple[torch.Tensor, ...]:
    """Convert list of experiences to tensors"""
    batch = Experience(*zip(*experiences))

    states = torch.FloatTensor(np.array(batch.state)).to(device)
    actions = torch.LongTensor(batch.action).to(device)
    rewards = torch.FloatTensor(batch.reward).to(device)
    next_states = torch.FloatTensor(np.array(batch.next_state)).to(device)
    dones = torch.FloatTensor(batch.done).to(device)

    return states, actions, rewards, next_states, dones