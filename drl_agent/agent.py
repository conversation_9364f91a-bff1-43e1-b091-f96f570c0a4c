"""
Deep Reinforcement Learning Agent for AMCFF-RL
Implements DQN with various enhancements for automotive fuzzing
"""
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
import logging
import os
from collections import deque

from .dqn_network import DuelingDQN, CategoricalDQN
from .replay_buffer import PrioritizedReplayBuffer, experience_to_tensors

logger = logging.getLogger(__name__)

class DQNAgent:
    """DQN Agent with enhancements for CAN bus fuzzing"""

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize DQN Agent

        Args:
            config: Configuration dictionary containing:
                - state_dim: Dimension of state space
                - action_dim: Dimension of action space
                - feature_dims: Dictionary of feature dimensions
                - learning_rate: Learning rate
                - gamma: Discount factor
                - epsilon_start: Initial exploration rate
                - epsilon_end: Final exploration rate
                - epsilon_decay: Exploration decay rate
                - buffer_size: Replay buffer size
                - batch_size: Training batch size
                - target_update_freq: Target network update frequency
                - device: torch device (cuda/cpu)
        """
        self.config = config
        self.state_dim = config['state_dim']
        self.action_dim = config['action_dim']
        self.device = torch.device(config.get('device', 'cuda' if torch.cuda.is_available() else 'cpu'))

        # Networks
        self.policy_net = DuelingDQN(
            self.state_dim,
            self.action_dim,
            config['feature_dims']
        ).to(self.device)

        self.target_net = DuelingDQN(
            self.state_dim,
            self.action_dim,
            config['feature_dims']
        ).to(self.device)

        self.target_net.load_state_dict(self.policy_net.state_dict())
        self.target_net.eval()

        # Optimizer
        self.optimizer = optim.Adam(
            self.policy_net.parameters(),
            lr=config.get('learning_rate', 1e-4)
        )

        # Learning parameters
        self.gamma = config.get('gamma', 0.99)
        self.epsilon_start = config.get('epsilon_start', 1.0)
        self.epsilon_end = config.get('epsilon_end', 0.01)
        self.epsilon_decay = config.get('epsilon_decay', 0.995)
        self.epsilon = self.epsilon_start

        # Replay buffer
        self.memory = PrioritizedReplayBuffer(
            capacity=config.get('buffer_size', 100000),
            alpha=config.get('priority_alpha', 0.6),
            beta_start=config.get('priority_beta_start', 0.4)
        )

        # Training parameters
        self.batch_size = config.get('batch_size', 32)
        self.target_update_freq = config.get('target_update_freq', 1000)
        self.training_steps = 0

        # Action space mapping (for hierarchical actions)
        self.action_space = self._build_action_space()

        # Statistics
        self.episode_rewards = deque(maxlen=100)
        self.episode_lengths = deque(maxlen=100)
        self.losses = deque(maxlen=1000)

        logger.info(f"DQN Agent initialized on device: {self.device}")

    def _build_action_space(self) -> Dict[int, Dict[str, Any]]:
        """Build hierarchical action space mapping"""
        action_space = {}
        action_idx = 0

        # Define action templates
        operation_types = ['value_manipulation', 'bit_flipping', 'timing_modification',
                          'message_injection', 'sequence_manipulation']
        target_messages = list(range(0x100, 0x110))  # Simplified for demo

        for msg_id in target_messages:
            for op_type in operation_types:
                action_space[action_idx] = {
                    'type': op_type,
                    'target_id': msg_id,
                    'parameters': self._get_default_parameters(op_type)
                }
                action_idx += 1

        return action_space

    def _get_default_parameters(self, operation_type: str) -> Dict[str, Any]:
        """Get default parameters for each operation type"""
        if operation_type == 'value_manipulation':
            return {'signal': 'default', 'value': 0.0}
        elif operation_type == 'bit_flipping':
            return {'bit_positions': [0]}
        elif operation_type == 'timing_modification':
            return {'delay': 0.01}
        elif operation_type == 'message_injection':
            return {'data': bytes(8)}
        elif operation_type == 'sequence_manipulation':
            return {'sequence': []}
        return {}

    def select_action(self, state: np.ndarray, explore: bool = True) -> Tuple[int, Dict[str, Any]]:
        """
        Select action using epsilon-greedy policy

        Args:
            state: Current state
            explore: Whether to use exploration

        Returns:
            action_idx: Index of selected action
            action_dict: Dictionary describing the action
        """
        # Epsilon-greedy exploration
        if explore and np.random.random() < self.epsilon:
            action_idx = np.random.randint(0, self.action_dim)
        else:
            with torch.no_grad():
                state_tensor = torch.FloatTensor(state).unsqueeze(0).to(self.device)
                q_values = self.policy_net(state_tensor)
                action_idx = q_values.argmax(1).item()

        # Map to action dictionary
        if action_idx in self.action_space:
            action_dict = self.action_space[action_idx].copy()

            # Add some randomization to parameters
            if explore and np.random.random() < 0.3:
                action_dict = self._randomize_action_parameters(action_dict)
        else:
            # Fallback action
            action_dict = {
                'type': 'message_injection',
                'target_id': 0x100,
                'parameters': {'data': bytes(8)}
            }

        return action_idx, action_dict

    def _randomize_action_parameters(self, action: Dict[str, Any]) -> Dict[str, Any]:
        """Add randomization to action parameters for exploration"""
        action_type = action['type']

        if action_type == 'value_manipulation':
            # Randomize signal value
            action['parameters']['value'] = np.random.uniform(0, 100)

        elif action_type == 'bit_flipping':
            # Randomize bit positions
            num_bits = np.random.randint(1, 4)
            action['parameters']['bit_positions'] = np.random.choice(64, num_bits, replace=False).tolist()

        elif action_type == 'timing_modification':
            # Randomize delay
            action['parameters']['delay'] = np.random.uniform(-0.05, 0.05)

        elif action_type == 'message_injection':
            # Randomize data bytes
            action['parameters']['data'] = bytes(np.random.randint(0, 256, 8))

        return action

    def store_experience(self, state: np.ndarray, action_idx: int, reward: float,
                        next_state: np.ndarray, done: bool):
        """Store experience in replay buffer"""
        self.memory.push(state, action_idx, reward, next_state, done)

    def update(self) -> Optional[float]:
        """
        Perform one training update

        Returns:
            loss: Training loss if update performed, None otherwise
        """
        if len(self.memory) < self.batch_size:
            return None

        # Sample batch
        experiences, weights, indices = self.memory.sample(self.batch_size)
        states, actions, rewards, next_states, dones = experience_to_tensors(experiences, self.device)
        weights = torch.FloatTensor(weights).to(self.device)

        # Current Q values
        current_q_values = self.policy_net(states).gather(1, actions.unsqueeze(1))

        # Next Q values using double DQN
        with torch.no_grad():
            # Select actions using policy network
            next_actions = self.policy_net(next_states).argmax(1, keepdim=True)
            # Evaluate using target network
            next_q_values = self.target_net(next_states).gather(1, next_actions).squeeze()

            # Compute targets
            targets = rewards + self.gamma * next_q_values * (1 - dones)

        # Compute loss with importance weights
        td_errors = targets.unsqueeze(1) - current_q_values
        loss = (weights * td_errors.pow(2)).mean()

        # Update priorities
        priorities = td_errors.detach().abs().cpu().numpy() + 1e-6
        self.memory.update_priorities(indices, priorities)

        # Optimize
        self.optimizer.zero_grad()
        loss.backward()

        # Gradient clipping
        torch.nn.utils.clip_grad_norm_(self.policy_net.parameters(), 10)

        self.optimizer.step()

        # Update target network
        self.training_steps += 1
        if self.training_steps % self.target_update_freq == 0:
            self.target_net.load_state_dict(self.policy_net.state_dict())
            logger.info(f"Target network updated at step {self.training_steps}")

        # Update exploration rate
        self.epsilon = max(self.epsilon_end, self.epsilon * self.epsilon_decay)

        # Reset noise in network
        if hasattr(self.policy_net, 'reset_noise'):
            self.policy_net.reset_noise()

        self.losses.append(loss.item())

        return loss.item()

    def get_statistics(self) -> Dict[str, float]:
        """Get agent statistics"""
        stats = {
            'epsilon': self.epsilon,
            'training_steps': self.training_steps,
            'buffer_size': len(self.memory),
            'avg_loss': np.mean(self.losses) if self.losses else 0.0,
            'avg_episode_reward': np.mean(self.episode_rewards) if self.episode_rewards else 0.0,
            'avg_episode_length': np.mean(self.episode_lengths) if self.episode_lengths else 0.0
        }
        return stats

    def save(self, filepath: str):
        """Save agent state"""
        checkpoint = {
            'policy_net_state_dict': self.policy_net.state_dict(),
            'target_net_state_dict': self.target_net.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'epsilon': self.epsilon,
            'training_steps': self.training_steps,
            'config': self.config
        }
        torch.save(checkpoint, filepath)
        logger.info(f"Agent saved to {filepath}")

    def load(self, filepath: str):
        """Load agent state"""
        checkpoint = torch.load(filepath, map_location=self.device)

        self.policy_net.load_state_dict(checkpoint['policy_net_state_dict'])
        self.target_net.load_state_dict(checkpoint['target_net_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        self.epsilon = checkpoint['epsilon']
        self.training_steps = checkpoint['training_steps']

        logger.info(f"Agent loaded from {filepath}")

class HierarchicalDQNAgent(DQNAgent):
    """Hierarchical DQN for structured action spaces"""

    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)

        # Additional networks for hierarchical decisions
        self.operation_net = DuelingDQN(
            self.state_dim,
            len(set(a['type'] for a in self.action_space.values())),
            config['feature_dims']
        ).to(self.device)

        self.target_selection_net = DuelingDQN(
            self.state_dim,
            len(set(a['target_id'] for a in self.action_space.values())),
            config['feature_dims']
        ).to(self.device)

    def select_hierarchical_action(self, state: np.ndarray, explore: bool = True) -> Tuple[int, Dict[str, Any]]:
        """Select action using hierarchical policy"""
        state_tensor = torch.FloatTensor(state).unsqueeze(0).to(self.device)

        # First, select operation type
        if explore and np.random.random() < self.epsilon:
            operation_idx = np.random.randint(0, self.operation_net.action_dim)
        else:
            with torch.no_grad():
                operation_q_values = self.operation_net(state_tensor)
                operation_idx = operation_q_values.argmax(1).item()

        # Then, select target
        if explore and np.random.random() < self.epsilon:
            target_idx = np.random.randint(0, self.target_selection_net.action_dim)
        else:
            with torch.no_grad():
                target_q_values = self.target_selection_net(state_tensor)
                target_idx = target_q_values.argmax(1).item()

        # Map to flat action space
        # This is simplified - in practice would need proper mapping
        action_idx = operation_idx * self.target_selection_net.action_dim + target_idx
        action_idx = min(action_idx, self.action_dim - 1)

        return super().select_action(state, explore=False)